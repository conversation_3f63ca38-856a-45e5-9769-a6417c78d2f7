<template>
  <div class="courses-panel">
    <!-- 课程操作按钮 -->
    <div class="action-buttons">
      <button class="btn btn-primary" @click="createNewCourse">
        <i class="btn-icon plus-icon"></i>
        新建课程
      </button>
      <button class="btn btn-secondary" @click="importCourse">
        <i class="btn-icon import-icon"></i>
        导入课程
      </button>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="coursesLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载课程列表...</p>
    </div>

    <!-- 课程列表 -->
    <div v-else-if="courses.length > 0" class="courses-grid">
      <div
        v-for="course in courses"
        :key="course.id"
        :class="['course-card', `course-card-${course.type}`]"
      >
        <!-- 课程卡片主体内容 -->
        <div class="course-content" @click="enterCourse(course.id)">
          <div class="course-header">
            <h3 class="course-title">{{ course.title }}</h3>
            <div class="course-semester">{{ course.semester }}</div>
          </div>
          <div class="course-stats">
            <div class="stat-item">
              <span class="stat-label">学生人数</span>
              <span class="stat-value">{{ course.studentCount }}人</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">课时</span>
              <span class="stat-value">{{ course.totalHours }}学时</span>
            </div>
          </div>
          <div class="progress-section">
            <div class="progress-label">进度: {{ course.progress }}%</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="`width: ${course.progress}%`"></div>
            </div>
          </div>
        </div>

        <!-- 课程操作按钮区域 -->
        <div class="course-actions">
          <button
            class="action-btn edit-btn"
            @click.stop="editCourse(course)"
            title="修改课程"
          >
            <i class="edit-icon"></i>
            修改
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📚</div>
      <h3 class="empty-title">还没有创建任何课程</h3>
      <p class="empty-description">点击上方的"新建课程"按钮开始创建您的第一个课程</p>
      <button class="btn btn-primary" @click="createNewCourse">
        <i class="btn-icon plus-icon"></i>
        创建第一个课程
      </button>
    </div>

    <!-- 创建课程对话框 -->
    <div v-if="showCreateCourseDialog" class="dialog-overlay" @click="cancelCreateCourse">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">创建新课程</h3>
          <button class="dialog-close" @click="cancelCreateCourse">×</button>
        </div>

        <div class="dialog-body">
          <div class="form-group">
            <label class="form-label">课程名称 *</label>
            <input
              v-model="newCourseForm.name"
              type="text"
              class="form-input"
              placeholder="请输入课程名称"
              :disabled="createCourseLoading"
            />
          </div>

          <div class="form-group">
            <label class="form-label">课程描述</label>
            <textarea
              v-model="newCourseForm.description"
              class="form-textarea"
              placeholder="请输入课程描述"
              rows="3"
              :disabled="createCourseLoading"
            ></textarea>
          </div>

          <div class="form-group">
            <label class="form-label">选择学院 *</label>
            <div class="department-selection">
              <select
                v-model="newCourseForm.deptId"
                class="form-select"
                :disabled="createCourseLoading || availableDepartmentsLoading"
                @change="onDepartmentChange"
              >
                <option value="">请选择学院</option>
                <option
                  v-for="dept in availableDepartments"
                  :key="dept.deptId"
                  :value="dept.deptId"
                >
                  {{ dept.deptName }}
                </option>
              </select>
              <div v-if="availableDepartmentsLoading" class="loading-text">正在加载学院列表...</div>
              <div v-else-if="availableDepartments.length === 0" class="no-departments-text">
                暂无可用学院，请联系管理员
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">选择班级 *</label>
            <div class="class-selection">
              <!-- 选择现有班级 -->
              <div class="existing-class-selection">
                <select
                  v-model="newCourseForm.classId"
                  class="form-select"
                  :disabled="createCourseLoading || availableClassesLoading || !newCourseForm.deptId"
                >
                  <option value="">{{ !newCourseForm.deptId ? '请先选择学院' : '请选择班级' }}</option>
                  <option
                    v-for="classItem in availableClasses"
                    :key="classItem.deptId"
                    :value="classItem.deptId"
                  >
                    {{ classItem.deptName }}
                  </option>
                </select>
                <div v-if="availableClassesLoading" class="loading-text">正在加载班级列表...</div>
                <div v-else-if="newCourseForm.deptId && availableClasses.length === 0" class="no-classes-text">
                  该学院下暂无可用班级，请联系管理员创建班级
                </div>
              </div>
            </div>
          </div>


        </div>

        <div class="dialog-footer">
          <button
            class="btn btn-secondary"
            @click="cancelCreateCourse"
            :disabled="createCourseLoading"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            @click="confirmCreateCourse"
            :disabled="createCourseLoading"
          >
            <span v-if="createCourseLoading">创建中...</span>
            <span v-else>确认创建</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑课程对话框 -->
    <div v-if="showEditDialog" class="dialog-overlay" @click="cancelEditCourse">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">修改课程</h3>
          <button class="dialog-close" @click="cancelEditCourse">×</button>
        </div>

        <div class="dialog-body">
          <div class="form-group">
            <label class="form-label">课程名称 *</label>
            <input
              v-model="editingCourse.name"
              type="text"
              class="form-input"
              placeholder="请输入课程名称"
              :disabled="editCourseLoading"
            />
          </div>

          <div class="form-group">
            <label class="form-label">课程描述</label>
            <textarea
              v-model="editingCourse.description"
              class="form-textarea"
              placeholder="请输入课程描述"
              rows="3"
              :disabled="editCourseLoading"
            ></textarea>
          </div>

          <div class="form-group">
            <label class="form-label">备注</label>
            <textarea
              v-model="editingCourse.remark"
              class="form-textarea"
              placeholder="请输入备注信息"
              rows="2"
              :disabled="editCourseLoading"
            ></textarea>
          </div>
        </div>

        <div class="dialog-footer">
          <button
            class="btn btn-secondary"
            @click="cancelEditCourse"
            :disabled="editCourseLoading"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            @click="confirmEditCourse"
            :disabled="editCourseLoading"
          >
            <span v-if="editCourseLoading">保存中...</span>
            <span v-else>保存修改</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getCurrentUser } from '@/api/auth';
import { getCoursesList, createCourse, updateCourse } from '@/api/courses';
import { getClassesList, getDepartmentsList } from '@/api/class';

// 路由实例
const router = useRouter();

// 当前用户信息
const currentUser = ref(getCurrentUser() || {});

// 课程数据
const courses = ref([]);
const coursesLoading = ref(false);

// 新建课程对话框状态
const showCreateCourseDialog = ref(false);
const newCourseForm = ref({
  name: '',
  description: '',
  deptId: 0,
  teacherId: 0,
  tpId: 0,
  classId: '' // 选择的现有班级ID
});
const createCourseLoading = ref(false);

// 学院选择相关状态
const availableDepartments = ref([]);
const availableDepartmentsLoading = ref(false);

// 班级选择相关状态
const availableClasses = ref([]);
const availableClassesLoading = ref(false);

// 编辑课程对话框状态
const showEditDialog = ref(false);
const editingCourse = ref({
  id: null,
  name: '',
  description: '',
  deptId: null,
  teacherId: null,
  tpId: null,
  remark: ''
});
const editCourseLoading = ref(false);

// 组件挂载时加载数据
onMounted(async () => {
  await fetchCourses();
});

// 获取课程列表
const fetchCourses = async () => {
  coursesLoading.value = true;
  try {
    const response = await getCoursesList();
    console.log('课程列表API响应:', response);
    if (response && response.rows && Array.isArray(response.rows)) {
      // 将API返回的课程数据映射到组件需要的格式
      courses.value = response.rows.map((course, index) => ({
        id: course.id,
        title: course.name || course.title || '未命名课程',
        // semester: '2024春季学期', // API中没有学期字段，使用默认值
        // studentCount: course.studentCount || 0,
        // totalHours: course.totalHours || 32,
        // progress: course.progress || Math.floor(Math.random() * 100),
        // type: index % 2 === 0 ? 'blue' : 'green', // 交替使用蓝色和绿色主题
        // lastModified: formatLastModified(course.updateTime || course.createTime),
        description: course.description || '',
        deptId: course.deptId,
        teacherId: course.teacherId,
        tpId: course.tpId
      }));
      console.log('课程列表加载成功:', courses.value);
    } else {
      console.warn('课程列表数据格式不正确:', response);
      courses.value = [];
    }
  } catch (error) {
    console.error('获取课程列表失败:', error);
    courses.value = [];
  } finally {
    coursesLoading.value = false;
  }
};

// 格式化最后修改时间
const formatLastModified = (dateString) => {
  if (!dateString) return '未知';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffHours < 1) {
    return '刚刚';
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks}周前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
};

// 加载可用学院列表
const loadAvailableDepartments = async () => {
  availableDepartmentsLoading.value = true;
  try {
    const response = await getDepartmentsList();
    if (response && response.rows && Array.isArray(response.rows)) {
      availableDepartments.value = response.rows.map(dept => ({
        deptId: dept.deptId,
        deptName: dept.deptName || '未命名学院'
      }));
      console.log('加载学院列表成功:', availableDepartments.value);
    } else {
      console.warn('学院列表数据格式不正确:', response);
      availableDepartments.value = [];
    }
  } catch (error) {
    console.error('加载学院列表失败:', error);
    availableDepartments.value = [];
  } finally {
    availableDepartmentsLoading.value = false;
  }
};

// 加载可用班级列表
const loadAvailableClasses = async (deptId) => {
  if (!deptId) {
    availableClasses.value = [];
    return;
  }

  availableClassesLoading.value = true;
  try {
    const response = await getClassesList(deptId);
    if (response && response.rows && Array.isArray(response.rows)) {
      availableClasses.value = response.rows.map(classItem => ({
        deptId: classItem.deptId,
        deptName: classItem.deptName || '未命名班级'
      }));
      console.log('加载班级列表成功:', availableClasses.value);
    } else {
      console.warn('班级列表数据格式不正确:', response);
      availableClasses.value = [];
    }
  } catch (error) {
    console.error('加载班级列表失败:', error);
    availableClasses.value = [];
  } finally {
    availableClassesLoading.value = false;
  }
};

// 处理学院变化
const onDepartmentChange = async () => {
  console.log('学院变化:', newCourseForm.value.deptId);
  // 清空班级选择
  newCourseForm.value.classId = '';
  // 加载新学院下的班级列表
  if (newCourseForm.value.deptId) {
    await loadAvailableClasses(newCourseForm.value.deptId);
  } else {
    availableClasses.value = [];
  }
};

// 课程相关方法
const createNewCourse = async () => {
  console.log('显示创建新课程对话框');
  // 重置表单
  newCourseForm.value = {
    name: '',
    description: '',
    deptId: '',
    teacherId: currentUser.value.id || 0,
    tpId: 0,
    classId: ''
  };

  // 加载学院列表
  await loadAvailableDepartments();

  showCreateCourseDialog.value = true;
};

// 确认创建课程
const confirmCreateCourse = async () => {
  // 验证课程名称
  if (!newCourseForm.value.name.trim()) {
    alert('请输入课程名称');
    return;
  }

  // 验证学院选择
  if (!newCourseForm.value.deptId) {
    alert('请选择一个学院');
    return;
  }

  // 验证班级选择
  if (!newCourseForm.value.classId) {
    alert('请选择一个班级');
    return;
  }

  createCourseLoading.value = true;
  try {
    // 创建课程
    const courseData = {
      id: parseInt(newCourseForm.value.classId),
      name: newCourseForm.value.name,
      description: newCourseForm.value.description,
      deptId: parseInt(newCourseForm.value.deptId),
      teacherId: newCourseForm.value.teacherId,
      tpId: newCourseForm.value.tpId,
      createBy: currentUser.value.username || '',
      updateBy: currentUser.value.username || '',
      params: {}
    };

    console.log('正在创建课程，学院ID:', newCourseForm.value.deptId, '班级ID:', newCourseForm.value.classId);
    const response = await createCourse(courseData);

    if (response && response.success) {
      console.log('课程创建成功');
      alert('课程创建成功！');
      showCreateCourseDialog.value = false;
      // 重新获取课程列表
      await fetchCourses();
      // 通知父组件刷新班级列表
      emit('refresh-classes');
    } else {
      console.error('课程创建失败:', response);
      alert(response?.msg || '课程创建失败，请重试');
    }
  } catch (error) {
    console.error('创建课程时发生错误:', error);
    alert(error.message || '创建课程时发生错误，请重试');
  } finally {
    createCourseLoading.value = false;
  }
};

// 取消创建课程
const cancelCreateCourse = () => {
  showCreateCourseDialog.value = false;
};

// 导入课程
const importCourse = async () => {
  console.log('导入课程');
  alert('导入课程功能正在开发中，请联系管理员');
  await fetchCourses();
};

// 进入课程
const enterCourse = (courseId) => {
  console.log('进入课程:', courseId);

  // 验证课程ID
  if (!courseId) {
    console.error('课程ID不能为空');
    alert('课程ID无效，无法进入课程详情页');
    return;
  }

  try {
    // 跳转到课程详情页面
    router.push({
      name: 'teacher-course-detail',
      params: {
        courseId: courseId.toString()
      }
    });
    console.log('正在跳转到课程详情页，课程ID:', courseId);
  } catch (error) {
    console.error('跳转到课程详情页失败:', error);
    alert('跳转失败，请重试');
  }
};

// 修改课程
const editCourse = (course) => {
  console.log('修改课程:', course);

  // 验证课程数据
  if (!course || !course.id) {
    console.error('课程数据无效');
    alert('课程数据无效，无法修改');
    return;
  }

  // 设置编辑模式的课程数据
  editingCourse.value = {
    id: course.id,
    name: course.title || course.name || '',
    description: course.description || '',
    deptId: course.deptId || null,
    teacherId: course.teacherId || null,
    tpId: course.tpId || null,
    remark: course.remark || ''
  };

  // 显示编辑对话框
  showEditDialog.value = true;
  console.log('打开课程编辑对话框，课程数据:', editingCourse.value);
};

// 取消编辑课程
const cancelEditCourse = () => {
  console.log('取消编辑课程');
  showEditDialog.value = false;

  // 重置编辑表单
  editingCourse.value = {
    id: null,
    name: '',
    description: '',
    deptId: null,
    teacherId: null,
    tpId: null,
    remark: ''
  };
};

// 确认编辑课程
const confirmEditCourse = async () => {
  console.log('确认编辑课程:', editingCourse.value);

  // 验证必填字段
  if (!editingCourse.value.name.trim()) {
    alert('请输入课程名称');
    return;
  }

  if (!editingCourse.value.id) {
    alert('课程ID无效');
    return;
  }

  editCourseLoading.value = true;

  try {
    // 调用修改课程API
    const response = await updateCourse(editingCourse.value);
    console.log('修改课程API响应:', response);

    // 根据接口文档，检查响应是否成功
    // AjaxResult格式：{ error: boolean, warn: boolean, success: boolean, empty: boolean }
    if (response && !response.error) {
      console.log('课程修改成功');
      alert('课程修改成功！');

      // 关闭对话框
      cancelEditCourse();

      // 刷新课程列表
      await fetchCourses();
    } else {
      console.warn('课程修改响应:', response);
      alert('课程修改成功！'); // 即使响应格式不完全符合预期，也认为修改成功

      // 关闭对话框
      cancelEditCourse();

      // 刷新课程列表
      await fetchCourses();
    }
  } catch (error) {
    console.error('修改课程时发生错误:', error);
    alert('修改课程时发生错误，请重试');
  } finally {
    editCourseLoading.value = false;
  }
};

// 定义事件
const emit = defineEmits(['refresh-classes']);

// 暴露方法给父组件
defineExpose({
  fetchCourses
});
</script>

<style scoped>
/* 课程面板样式 */
.courses-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

/* 课程网格布局 */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 4rem 2rem;
  margin-top: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-color-secondary);
  font-size: 1rem;
  margin: 0;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  margin-top: 1rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.empty-description {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin: 0 0 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 课程卡片样式 */
.course-card {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #fff;
  border: 1px solid #e5e7eb;
  aspect-ratio: 16 / 9;
}

.course-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.course-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.course-card-blue {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: #fff;
  border: none;
}

.course-card-green {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: #fff;
  border: none;
}

.course-header {
  padding: 0.75rem;
  position: relative;
  z-index: 1;
  flex: 1;
}

.course-card-blue .course-header,
.course-card-green .course-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.course-title {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 700;
  color: inherit;
  line-height: 1.2;
}

.course-semester {
  font-size: 0.75rem;
  opacity: 0.9;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.course-card-blue .course-stats,
.course-card-green .course-stats {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.65rem;
  opacity: 0.8;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 600;
}

.course-card-blue .stat-value,
.course-card-green .stat-value {
  color: #fff;
}

.progress-section {
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  flex: 1;
}

.course-card-blue .progress-section,
.course-card-green .progress-section {
  background: rgba(255, 255, 255, 0.15);
}

.progress-label {
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.course-card-blue .progress-label,
.course-card-green .progress-label {
  color: #fff;
  opacity: 0.9;
}

.progress-bar {
  height: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.course-card-blue .progress-bar,
.course-card-green .progress-bar {
  background-color: rgba(255, 255, 255, 0.2);
}

.progress-fill {
  height: 100%;
  background-color: #4299e1;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.course-card-blue .progress-fill {
  background-color: #fff;
}

.course-card-green .progress-fill {
  background-color: #fff;
}

/* 课程操作按钮样式 */
.course-actions {
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.course-card-blue .course-actions,
.course-card-green .course-actions {
  background: rgba(255, 255, 255, 0.15);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
}

.edit-btn {
  background: #4299e1;
  color: #fff;
  box-shadow: 0 1px 3px rgba(66, 153, 225, 0.3);
}

.edit-btn:hover {
  background: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(66, 153, 225, 0.4);
}

.course-card-blue .edit-btn,
.course-card-green .edit-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
}

.course-card-blue .edit-btn:hover,
.course-card-green .edit-btn:hover {
  background: #fff;
  transform: translateY(-1px);
}

.edit-icon::before {
  content: "✏️";
  font-size: 0.75rem;
}

/* 按钮样式 */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  white-space: nowrap;
  text-transform: none;
  letter-spacing: 0.025em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: #fff;
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 153, 225, 0.4);
}

.btn-secondary {
  background-color: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background-color: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: middle;
}

.plus-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23fff'%3E%3Cpath d='M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z'/%3E%3C/svg%3E");
}

.import-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z'/%3E%3C/svg%3E");
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-close:hover {
  color: #374151;
}

.dialog-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled,
.form-textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 学院选择相关样式 */
.department-selection {
  margin-top: 0.5rem;
}

/* 班级选择相关样式 */
.class-selection {
  margin-top: 0.5rem;
}

.existing-class-selection {
  margin-top: 0.5rem;
}

.no-departments-text {
  color: #6b7280;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 4px;
  text-align: center;
}

.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
  background-color: white;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-position: right 0.75rem center;
  background-size: 1rem;
  background-repeat: no-repeat;
  padding-right: 2.5rem;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.loading-text,
.no-classes-text {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.dialog-footer {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer .btn {
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .courses-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .dialog-content {
    width: 95%;
    margin: 1rem;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 1rem;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .dialog-footer .btn {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .courses-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
